{"name": "selfhood.studio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-slot": "^1.2.0", "@react-three/drei": "^10.0.6", "@react-three/fiber": "^9.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.487.0", "maath": "^0.10.8", "marked": "^15.0.8", "next": "15.3.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "shadcn": "^2.4.0", "tailwind-merge": "^3.2.0", "three": "^0.175.0", "tw-animate-css": "^1.2.5"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}, "optionalDependencies": {"@tailwindcss/oxide-linux-x64-gnu": "^4.0.6", "lightningcss-linux-x64-gnu": "^1.24.1"}}